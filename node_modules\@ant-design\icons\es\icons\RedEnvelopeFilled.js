import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RedEnvelopeFilledSvg from "@ant-design/icons-svg/es/asn/RedEnvelopeFilled";
import AntdIcon from "../components/AntdIcon";
var RedEnvelopeFilled = function RedEnvelopeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RedEnvelopeFilledSvg
  }));
};

/**![red-envelope](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTY0NyA0NzAuNGwtODcuMiAxNjFoNDUuOWM0LjYgMCA4LjQgMy44IDguNCA4LjR2MjUuMWMwIDQuNi0zLjggOC40LTguNCA4LjRoLTYzLjN2MjguNmg2My4zYzQuNiAwIDguNCAzLjggOC40IDguNHYyNWMuMiA0LjYtMy42IDguNS04LjIgOC41aC02My4zdjQ5LjljMCA0LjYtMy44IDguNC04LjQgOC40aC00My43Yy00LjYgMC04LjQtMy44LTguNC04LjR2LTQ5LjloLTYzYy00LjYgMC04LjQtMy44LTguNC04LjR2LTI1LjFjMC00LjYgMy44LTguNCA4LjQtOC40aDYzdi0yOC42aC02M2MtNC42IDAtOC40LTMuOC04LjQtOC40di0yNS4xYzAtNC42IDMuOC04LjQgOC40LTguNGg0NS40bC04Ny41LTE2MWMtMi4yLTQuMS0uNy05LjEgMy40LTExLjQgMS4zLS42IDIuNi0xIDMuOS0xaDQ4LjhjMy4yIDAgNi4xIDEuOCA3LjUgNC42bDcxLjkgMTQxLjggNzEuOS0xNDEuOWE4LjUgOC41IDAgMDE3LjUtNC42aDQ3LjhjNC42IDAgOC40IDMuOCA4LjQgOC40LS4xIDEuNS0uNSAyLjktMS4xIDQuMXpNNTEyLjYgMzIzTDI4OSAxNDhoNDQ2TDUxMi42IDMyM3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(RedEnvelopeFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RedEnvelopeFilled';
}
export default RefIcon;