import{Globals as t,createStringInterpolator as a,colors as p}from"@react-spring/shared";import{createHost as i}from"@react-spring/animated";var r=["Arc","Arrow","Circle","Ellipse","FastLayer","Group","Image","Label","Layer","Line","Path","Rect","RegularPolygon","Ring","Shape","Sprite","Star","Tag","Text","TextPath","Transformer","Wedge"];export*from"@react-spring/core";t.assign({createStringInterpolator:a,colors:p});var m=i(r,{applyAnimatedValues(e,o){if(!e.nodeType)return!1;e._applyProps(e,o)}}),x=m.animated;export{x as a,x as animated};
//# sourceMappingURL=react-spring_konva.modern.production.min.mjs.map