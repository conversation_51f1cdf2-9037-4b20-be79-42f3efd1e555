"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RetweetOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RetweetOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RetweetOutlined = function RetweetOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RetweetOutlined.default
  }));
};

/**![retweet](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzNiA1NTJoNjMuNmM0LjQgMCA4LTMuNiA4LThWMjg4LjdoNTI4LjZ2NzIuNmMwIDEuOS42IDMuNyAxLjggNS4yYTguMyA4LjMgMCAwMDExLjcgMS40TDg5MyAyNTUuNGM0LjMtNSAzLjYtMTAuMyAwLTEzLjJMNzQ5LjcgMTI5LjhhOC4yMiA4LjIyIDAgMDAtNS4yLTEuOGMtNC42IDAtOC40IDMuOC04LjQgOC40VjIwOUgxOTkuN2MtMzkuNSAwLTcxLjcgMzIuMi03MS43IDcxLjhWNTQ0YzAgNC40IDMuNiA4IDggOHptNzUyLTgwaC02My42Yy00LjQgMC04IDMuNi04IDh2MjU1LjNIMjg3Ljh2LTcyLjZjMC0xLjktLjYtMy43LTEuOC01LjJhOC4zIDguMyAwIDAwLTExLjctMS40TDEzMSA3NjguNmMtNC4zIDUtMy42IDEwLjMgMCAxMy4ybDE0My4zIDExMi40YzEuNSAxLjIgMy4zIDEuOCA1LjIgMS44IDQuNiAwIDguNC0zLjggOC40LTguNFY4MTVoNTM2LjZjMzkuNSAwIDcxLjctMzIuMiA3MS43LTcxLjhWNDgwYy0uMi00LjQtMy44LTgtOC4yLTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(RetweetOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RetweetOutlined';
}
var _default = exports.default = RefIcon;