type Timer = ReturnType<typeof setTimeout>;
type CachedKey = string | number;
export interface CachedData<TData = any, TParams = any> {
    data: TData;
    params: TParams;
    time: number;
}
interface RecordData extends CachedData {
    timer: Timer | undefined;
}
declare const setCache: (key: Cached<PERSON>ey, cacheTime: number, cachedData: CachedData) => void;
declare const getCache: (key: Cached<PERSON><PERSON>) => RecordData | undefined;
declare const clearCache: (key?: string | string[]) => void;
export { getCache, setCache, clearCache };
