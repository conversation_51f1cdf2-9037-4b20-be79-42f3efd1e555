export declare const isObject: (value: unknown) => value is Record<any, any>;
export declare const isFunction: (value: unknown) => value is (...args: any) => any;
export declare const isString: (value: unknown) => value is string;
export declare const isBoolean: (value: unknown) => value is boolean;
export declare const isNumber: (value: unknown) => value is number;
export declare const isUndef: (value: unknown) => value is undefined;
