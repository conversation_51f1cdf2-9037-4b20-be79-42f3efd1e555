"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RiseOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RiseOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RiseOutlined = function RiseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RiseOutlined.default
  }));
};

/**![rise](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxNyAyMTEuMWwtMTk5LjIgMjRjLTYuNi44LTkuNCA4LjktNC43IDEzLjZsNTkuMyA1OS4zLTIyNiAyMjYtMTAxLjgtMTAxLjdjLTYuMy02LjMtMTYuNC02LjItMjIuNiAwTDEwMC4zIDc1NC4xYTguMDMgOC4wMyAwIDAwMCAxMS4zbDQ1IDQ1LjJjMy4xIDMuMSA4LjIgMy4xIDExLjMgMEw0MzMuMyA1MzQgNTM1IDYzNS43YzYuMyA2LjIgMTYuNCA2LjIgMjIuNiAwTDgyOSAzNjQuNWw1OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDAxMy42LTQuN2wyNC0xOTkuMmMuNy01LjEtMy43LTkuNS04LjktOC44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(RiseOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RiseOutlined';
}
var _default = exports.default = RefIcon;