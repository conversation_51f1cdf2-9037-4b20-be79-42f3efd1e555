"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RightCircleOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RightCircleOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RightCircleOutlined = function RightCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RightCircleOutlined.default
  }));
};

/**![right-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY2Ni43IDUwNS41bC0yNDYtMTc4QTggOCAwIDAwNDA4IDMzNHY0Ni45YzAgMTAuMiA0LjkgMTkuOSAxMy4yIDI1LjlMNTY2LjYgNTEyIDQyMS4yIDYxNy4yYy04LjMgNi0xMy4yIDE1LjYtMTMuMiAyNS45VjY5MGMwIDYuNSA3LjQgMTAuMyAxMi43IDYuNWwyNDYtMTc4YzQuNC0zLjIgNC40LTkuOCAwLTEzeiIgLz48cGF0aCBkPSJNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTAgODIwYy0yMDUuNCAwLTM3Mi0xNjYuNi0zNzItMzcyczE2Ni42LTM3MiAzNzItMzcyIDM3MiAxNjYuNiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzIgMzcyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(RightCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightCircleOutlined';
}
var _default = exports.default = RefIcon;