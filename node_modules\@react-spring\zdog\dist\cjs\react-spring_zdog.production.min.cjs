"use strict";var f=Object.create;var m=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var E=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty;var P=(e,t)=>{for(var n in t)m(e,n,{get:t[n],enumerable:!0})},i=(e,t,n,g)=>{if(t&&typeof t=="object"||typeof t=="function")for(let d of c(t))!h.call(e,d)&&d!==n&&m(e,d,{get:()=>t[d],enumerable:!(g=x(t,d))||g.enumerable});return e},p=(e,t,n)=>(i(e,t,"default"),n&&i(n,t,"default")),u=(e,t,n)=>(n=e!=null?f(E(e)):{},i(t||!e||!e.__esModule?m(n,"default",{value:e,enumerable:!0}):n,e)),R=e=>i(m({},"__esModule",{value:!0}),e);var r={};P(r,{a:()=>v,animated:()=>v});module.exports=R(r);var l=require("react-zdog"),Z=require("@react-spring/core"),s=require("@react-spring/shared"),y=require("@react-spring/animated");var o=u(require("react-zdog")),a={Anchor:o.Anchor,Shape:o.Shape,Group:o.Group,Rect:o.Rect,RoundedRect:o.RoundedRect,Ellipse:o.Ellipse,Polygon:o.Polygon,Hemisphere:o.Hemisphere,Cylinder:o.Cylinder,Cone:o.Cone,Box:o.Box};p(r,require("@react-spring/core"),module.exports);Z.Globals.assign({createStringInterpolator:s.createStringInterpolator,colors:s.colors});var A=(0,y.createHost)(a,{applyAnimatedValues:l.applyProps}),v=A.animated;
//# sourceMappingURL=react-spring_zdog.production.min.cjs.map