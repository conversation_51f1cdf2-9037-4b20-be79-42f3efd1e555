"use strict";var s=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var v=Object.prototype.hasOwnProperty;var P=(r,e)=>{for(var a in e)s(r,a,{get:e[a],enumerable:!0})},m=(r,e,a,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of y(e))!v.call(r,i)&&i!==a&&s(r,i,{get:()=>e[i],enumerable:!(n=x(e,i))||n.enumerable});return r},t=(r,e,a)=>(m(r,e,"default"),a&&m(a,e,"default"));var d=r=>m(s({},"__esModule",{value:!0}),r);var o={};P(o,{a:()=>c,animated:()=>c});module.exports=d(o);var p=require("@react-spring/shared"),f=require("@react-spring/animated");var l=["Arc","Arrow","Circle","Ellipse","FastLayer","Group","Image","Label","Layer","Line","Path","Rect","RegularPolygon","Ring","Shape","Sprite","Star","Tag","Text","TextPath","Transformer","Wedge"];t(o,require("@react-spring/core"),module.exports);p.Globals.assign({createStringInterpolator:p.createStringInterpolator,colors:p.colors});var g=(0,f.createHost)(l,{applyAnimatedValues(r,e){if(!r.nodeType)return!1;r._applyProps(r,e)}}),c=g.animated;
//# sourceMappingURL=react-spring_konva.production.min.cjs.map