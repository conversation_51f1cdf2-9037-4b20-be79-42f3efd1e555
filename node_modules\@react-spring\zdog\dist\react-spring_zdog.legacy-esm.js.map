{"version": 3, "sources": ["../src/index.ts", "../src/primitives.ts"], "sourcesContent": ["import { applyProps } from 'react-zdog'\nimport { Globals } from '@react-spring/core'\nimport { createStringInterpolator, colors } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\nimport { primitives } from './primitives'\nimport { WithAnimated } from './animated'\n\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n})\n\nconst host = createHost(primitives, {\n  applyAnimatedValues: applyProps,\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import { ElementType } from 'react'\nimport * as <PERSON><PERSON> from 'react-zdog'\n\ntype ZdogExports = typeof Zdog\ntype ZdogElements = {\n  [P in keyof ZdogExports]: P extends 'Illustration'\n    ? never\n    : ZdogExports[P] extends ElementType\n      ? P\n      : never\n}[keyof ZdogExports]\n\nexport const primitives: { [key in ZdogElements]: ElementType } = {\n  Anchor: Zdog.Anchor,\n  Shape: Zdog.Shape,\n  Group: Zdog.Group,\n  Rect: Zdog.Rect,\n  RoundedRect: Zdog.RoundedRect,\n  Ellipse: Zdog.Ellipse,\n  Polygon: Zdog.Polygon,\n  Hemisphere: Zdog.Hemisphere,\n  Cylinder: Zdog.Cylinder,\n  Cone: Zdog.Cone,\n  Box: Zdog.Box,\n}\n"], "mappings": ";AAAA,SAAS,kBAAkB;AAC3B,SAAS,eAAe;AACxB,SAAS,0BAA0B,cAAc;AACjD,SAAS,kBAAkB;;;ACF3B,YAAY,UAAU;AAWf,IAAM,aAAqD;AAAA,EAChE,QAAa;AAAA,EACb,OAAY;AAAA,EACZ,OAAY;AAAA,EACZ,MAAW;AAAA,EACX,aAAkB;AAAA,EAClB,SAAc;AAAA,EACd,SAAc;AAAA,EACd,YAAiB;AAAA,EACjB,UAAe;AAAA,EACf,MAAW;AAAA,EACX,KAAU;AACZ;;;ADJA,cAAc;AAbd,QAAQ,OAAO;AAAA,EACb;AAAA,EACA;AACF,CAAC;AAED,IAAM,OAAO,WAAW,YAAY;AAAA,EAClC,qBAAqB;AACvB,CAAC;AAEM,IAAM,WAAW,KAAK;", "names": []}