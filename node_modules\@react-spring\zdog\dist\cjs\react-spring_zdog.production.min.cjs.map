{"version": 3, "sources": ["../../src/index.ts", "../../src/primitives.ts"], "sourcesContent": ["import { applyProps } from 'react-zdog'\nimport { Globals } from '@react-spring/core'\nimport { createStringInterpolator, colors } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\nimport { primitives } from './primitives'\nimport { WithAnimated } from './animated'\n\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n})\n\nconst host = createHost(primitives, {\n  applyAnimatedValues: applyProps,\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import { ElementType } from 'react'\nimport * as <PERSON><PERSON> from 'react-zdog'\n\ntype ZdogExports = typeof Zdog\ntype ZdogElements = {\n  [P in keyof ZdogExports]: P extends 'Illustration'\n    ? never\n    : ZdogExports[P] extends ElementType\n      ? P\n      : never\n}[keyof ZdogExports]\n\nexport const primitives: { [key in ZdogElements]: ElementType } = {\n  Anchor: Zdog.Anchor,\n  Shape: Zdog.Shape,\n  Group: Zdog.Group,\n  Rect: Zdog.Rect,\n  RoundedRect: Zdog.RoundedRect,\n  Ellipse: Zdog.Ellipse,\n  Polygon: Zdog.Polygon,\n  Hemisphere: Zdog.Hemisphere,\n  Cylinder: Zdog.Cylinder,\n  Cone: Zdog.Cone,\n  Box: Zdog.Box,\n}\n"], "mappings": "wmBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,OAAAE,EAAA,aAAAA,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA2B,sBAC3BC,EAAwB,8BACxBC,EAAiD,gCACjDC,EAA2B,kCCF3B,IAAAC,EAAsB,yBAWTC,EAAqD,CAChE,OAAa,SACb,MAAY,QACZ,MAAY,QACZ,KAAW,OACX,YAAkB,cAClB,QAAc,UACd,QAAc,UACd,WAAiB,aACjB,SAAe,WACf,KAAW,OACX,IAAU,KACZ,EDJAC,EAAAC,EAAc,8BApBd,gBAOA,UAAQ,OAAO,CACb,oDACA,eACF,CAAC,EAED,IAAMC,KAAO,cAAWC,EAAY,CAClC,oBAAqB,YACvB,CAAC,EAEYC,EAAWF,EAAK", "names": ["src_exports", "__export", "animated", "__toCommonJS", "import_react_zdog", "import_core", "import_shared", "import_animated", "Zdog", "primitives", "__reExport", "src_exports", "host", "primitives", "animated"]}