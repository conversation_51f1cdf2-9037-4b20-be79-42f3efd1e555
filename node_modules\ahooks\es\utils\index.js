export var isObject = function (value) {
  return value !== null && typeof value === 'object';
};
export var isFunction = function (value) {
  return typeof value === 'function';
};
export var isString = function (value) {
  return typeof value === 'string';
};
export var isBoolean = function (value) {
  return typeof value === 'boolean';
};
export var isNumber = function (value) {
  return typeof value === 'number';
};
export var isUndef = function (value) {
  return typeof value === 'undefined';
};