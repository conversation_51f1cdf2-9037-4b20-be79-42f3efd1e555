{"name": "@react-spring/konva", "version": "9.7.5", "module": "./dist/react-spring_konva.legacy-esm.js", "main": "./dist/cjs/index.js", "types": "./dist/react-spring_konva.modern.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/react-spring_konva.modern.d.ts", "import": "./dist/react-spring_konva.modern.mjs", "require": "./dist/cjs/index.js"}}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": "pmndrs/react-spring", "homepage": "https://github.com/pmndrs/react-spring#readme", "keywords": ["animated", "animation", "hooks", "motion", "react", "react-native", "spring", "typescript", "velocity"], "license": "MIT", "author": "<PERSON>", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)"], "dependencies": {"@react-spring/animated": "~9.7.5", "@react-spring/core": "~9.7.5", "@react-spring/shared": "~9.7.5", "@react-spring/types": "~9.7.5"}, "peerDependencies": {"konva": ">=2.6", "react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-konva": "^16.8.0 || ^16.8.7-0 || ^16.9.0-0 || ^16.10.1-0 || ^16.12.0-0 || ^16.13.0-0 || ^17.0.0-0 || ^17.0.1-0 || ^17.0.2-0 || ^18.0.0-0"}, "scripts": {"build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "dev": "tsup --watch", "lint": "TIMING=1 eslint \"src/**/*.ts*\"", "pack": "yarn pack"}}